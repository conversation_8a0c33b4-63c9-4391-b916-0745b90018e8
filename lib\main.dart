import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:wargani/screens/perfect_receipt_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(
    const ProviderScope(
      child: PerfectMarathiApp(),
    ),
  );
}

/// PERFECT Marathi Receipt App - सगळे issues fix करणारा
class PerfectMarathiApp extends StatelessWidget {
  const PerfectMarathiApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'PERFECT मराठी पावती',
      theme: ThemeData(
        primarySwatch: Colors.orange,
        fontFamily: 'Hind',
        useMaterial3: true,
      ),
      home: const PerfectReceiptScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}


